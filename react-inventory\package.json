{"name": "react-inventory", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^2.1.8", "axios": "^1.7.7", "classnames": "^2.5.1", "jspdf": "^2.5.2", "jspdf-autotable": "^3.8.4", "react": "^18.3.1", "react-datepicker": "^7.3.0", "react-dom": "^18.3.1", "react-icons": "^5.3.0", "react-modal": "^3.16.1", "react-router-dom": "^6.26.2", "recharts": "^2.13.0-alpha.5", "uuid": "^10.0.0"}, "devDependencies": {"@eslint/js": "^9.9.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.20", "eslint": "^9.9.0", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "postcss": "^8.4.45", "tailwindcss": "^3.4.10", "vite": "^5.4.1"}}