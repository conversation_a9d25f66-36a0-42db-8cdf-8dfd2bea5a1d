var $bNpOp$react = require("react");


function $parcel$interopDefault(a) {
  return a && a.__esModule ? a.default : a;
}

function $parcel$export(e, n, v, s) {
  Object.defineProperty(e, n, {get: v, set: s, enumerable: true, configurable: true});
}

$parcel$export(module.exports, "PressResponderContext", () => $01d3f539e91688c8$export$5165eccb35aaadb5);
/*
 * Copyright 2020 Adobe. All rights reserved.
 * This file is licensed to you under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License. You may obtain a copy
 * of the License at http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under
 * the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS
 * OF ANY KIND, either express or implied. See the License for the specific language
 * governing permissions and limitations under the License.
 */ 
const $01d3f539e91688c8$export$5165eccb35aaadb5 = (0, ($parcel$interopDefault($bNpOp$react))).createContext({
    register: ()=>{}
});
$01d3f539e91688c8$export$5165eccb35aaadb5.displayName = 'PressResponderContext';


//# sourceMappingURL=context.main.js.map
