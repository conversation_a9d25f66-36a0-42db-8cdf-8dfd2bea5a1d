<?xml version="1.0"?>
<ruleset
    name="Mess detection rules for Carbon"
    xmlns="http://pmd.sf.net/ruleset/1.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://pmd.sf.net/ruleset/1.0.0
             http://pmd.sf.net/ruleset_xml_schema.xsd"
    xsi:noNamespaceSchemaLocation="http://pmd.sf.net/ruleset_xml_schema.xsd"
>
    <description>
        Mess detection rules for Carbon
    </description>
    <rule ref="rulesets/cleancode.xml">
        <exclude name="BooleanArgumentFlag" />
        <exclude name="StaticAccess" />
        <exclude name="IfStatementAssignment" />
        <exclude name="UndefinedVariable" />
        <exclude name="ErrorControlOperator" />
    </rule>
    <rule ref="rulesets/controversial.xml" />
    <rule ref="rulesets/design.xml">
        <exclude name="EvalExpression" />
        <exclude name="CouplingBetweenObjects" />
        <exclude name="CountInLoopExpression" />
        <exclude name="DevelopmentCodeFragment" />
    </rule>
    <rule ref="rulesets/design.xml/DevelopmentCodeFragment">
        <properties>
            <property name="ignore-namespaces" value="true" />
        </properties>
    </rule>
    <rule ref="rulesets/unusedcode.xml">
        <exclude name="UnusedLocalVariable" />
        <exclude name="UnusedFormalParameter" />
    </rule>
</ruleset>
