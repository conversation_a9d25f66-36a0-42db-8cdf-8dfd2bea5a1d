name: Sponsors

on:
  schedule:
    - cron: '0 0 15 * *'
    - cron: '0 0 2 * *'

jobs:
  sponsors:
    name: Update readme

    runs-on: ubuntu-latest

    steps:
      - name: Checkout the code
        uses: actions/checkout@v4
        with:
          ref: master

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: 8.4
          tools: composer:v2

      - name: Get composer cache directory
        id: composer-cache
        shell: bash
        run: echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT

      - name: Cache composer dependencies
        uses: actions/cache@v4
        with:
          path: ${{ steps.composer-cache.outputs.dir }}
          key: "sponsors-${{ hashFiles('**/composer.json') }}"
          restore-keys: "sponsors-${{ hashFiles('**/composer.json') }}"

      - name: Install dependencies
        uses: nick-fields/retry@v3
        if: steps.composer-cache.outputs.cache-hit != 'true'
        with:
          timeout_minutes: 10
          max_attempts: 3
          command: composer update --prefer-dist --no-progress --prefer-stable

      - name: Update sponsors on readme.md
        run: composer sponsors

      - name: <PERSON><PERSON> Pull Request
        if: github.repository_owner == 'briannesbitt'
        uses: peter-evans/create-pull-request@v7
        with:
          branch: job/update-sponsors
          commit-message: Update sponsors
          title: Update sponsors
          body: Monthly automated sponsors update
          assignees: kylekatarnls
          reviewers: kylekatarnls
          add-paths: |
            readme.md
