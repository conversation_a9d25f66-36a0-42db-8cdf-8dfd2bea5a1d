<?php

declare(strict_types=1);

/**
 * This file is part of the Carbon package.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Tests\Localization;

use PHPUnit\Framework\Attributes\Group;

#[Group('localization')]
class SmnTest extends LocalizationTestCase
{
    public const LOCALE = 'smn'; // Inari Sami

    public const CASES = [
        // Carbon::parse('2018-01-04 00:00:00')->addDays(1)->calendar(Carbon::parse('2018-01-04 00:00:00'))
        'Tomorrow at 0.00',
        // Carbon::parse('2018-01-04 00:00:00')->addDays(2)->calendar(Carbon::parse('2018-01-04 00:00:00'))
        'lávurduv at 0.00',
        // Carbon::parse('2018-01-04 00:00:00')->addDays(3)->calendar(Carbon::parse('2018-01-04 00:00:00'))
        'pase<PERSON><PERSON><PERSON> at 0.00',
        // Carbon::parse('2018-01-04 00:00:00')->addDays(4)->calendar(Carbon::parse('2018-01-04 00:00:00'))
        'vuossaargâ at 0.00',
        // Carbon::parse('2018-01-04 00:00:00')->addDays(5)->calendar(Carbon::parse('2018-01-04 00:00:00'))
        'majebaargâ at 0.00',
        // Carbon::parse('2018-01-04 00:00:00')->addDays(6)->calendar(Carbon::parse('2018-01-04 00:00:00'))
        'koskoho at 0.00',
        // Carbon::parse('2018-01-05 00:00:00')->addDays(6)->calendar(Carbon::parse('2018-01-05 00:00:00'))
        'tuorâstuv at 0.00',
        // Carbon::parse('2018-01-06 00:00:00')->addDays(6)->calendar(Carbon::parse('2018-01-06 00:00:00'))
        'vástuppeeivi at 0.00',
        // Carbon::parse('2018-01-07 00:00:00')->addDays(2)->calendar(Carbon::parse('2018-01-07 00:00:00'))
        'majebaargâ at 0.00',
        // Carbon::parse('2018-01-07 00:00:00')->addDays(3)->calendar(Carbon::parse('2018-01-07 00:00:00'))
        'koskoho at 0.00',
        // Carbon::parse('2018-01-07 00:00:00')->addDays(4)->calendar(Carbon::parse('2018-01-07 00:00:00'))
        'tuorâstuv at 0.00',
        // Carbon::parse('2018-01-07 00:00:00')->addDays(5)->calendar(Carbon::parse('2018-01-07 00:00:00'))
        'vástuppeeivi at 0.00',
        // Carbon::parse('2018-01-07 00:00:00')->addDays(6)->calendar(Carbon::parse('2018-01-07 00:00:00'))
        'lávurduv at 0.00',
        // Carbon::now()->subDays(2)->calendar()
        'Last pasepeeivi at 20.49',
        // Carbon::parse('2018-01-04 00:00:00')->subHours(2)->calendar(Carbon::parse('2018-01-04 00:00:00'))
        'Yesterday at 22.00',
        // Carbon::parse('2018-01-04 12:00:00')->subHours(2)->calendar(Carbon::parse('2018-01-04 12:00:00'))
        'Today at 10.00',
        // Carbon::parse('2018-01-04 00:00:00')->addHours(2)->calendar(Carbon::parse('2018-01-04 00:00:00'))
        'Today at 2.00',
        // Carbon::parse('2018-01-04 23:00:00')->addHours(2)->calendar(Carbon::parse('2018-01-04 23:00:00'))
        'Tomorrow at 1.00',
        // Carbon::parse('2018-01-07 00:00:00')->addDays(2)->calendar(Carbon::parse('2018-01-07 00:00:00'))
        'majebaargâ at 0.00',
        // Carbon::parse('2018-01-08 00:00:00')->subDay()->calendar(Carbon::parse('2018-01-08 00:00:00'))
        'Yesterday at 0.00',
        // Carbon::parse('2018-01-04 00:00:00')->subDays(1)->calendar(Carbon::parse('2018-01-04 00:00:00'))
        'Yesterday at 0.00',
        // Carbon::parse('2018-01-04 00:00:00')->subDays(2)->calendar(Carbon::parse('2018-01-04 00:00:00'))
        'Last majebaargâ at 0.00',
        // Carbon::parse('2018-01-04 00:00:00')->subDays(3)->calendar(Carbon::parse('2018-01-04 00:00:00'))
        'Last vuossaargâ at 0.00',
        // Carbon::parse('2018-01-04 00:00:00')->subDays(4)->calendar(Carbon::parse('2018-01-04 00:00:00'))
        'Last pasepeeivi at 0.00',
        // Carbon::parse('2018-01-04 00:00:00')->subDays(5)->calendar(Carbon::parse('2018-01-04 00:00:00'))
        'Last lávurduv at 0.00',
        // Carbon::parse('2018-01-04 00:00:00')->subDays(6)->calendar(Carbon::parse('2018-01-04 00:00:00'))
        'Last vástuppeeivi at 0.00',
        // Carbon::parse('2018-01-03 00:00:00')->subDays(6)->calendar(Carbon::parse('2018-01-03 00:00:00'))
        'Last tuorâstuv at 0.00',
        // Carbon::parse('2018-01-02 00:00:00')->subDays(6)->calendar(Carbon::parse('2018-01-02 00:00:00'))
        'Last koskoho at 0.00',
        // Carbon::parse('2018-01-07 00:00:00')->subDays(2)->calendar(Carbon::parse('2018-01-07 00:00:00'))
        'Last vástuppeeivi at 0.00',
        // Carbon::parse('2018-01-01 00:00:00')->isoFormat('Qo Mo Do Wo wo')
        '1st 1st 1st 1st 1st',
        // Carbon::parse('2018-01-02 00:00:00')->isoFormat('Do wo')
        '2nd 1st',
        // Carbon::parse('2018-01-03 00:00:00')->isoFormat('Do wo')
        '3rd 1st',
        // Carbon::parse('2018-01-04 00:00:00')->isoFormat('Do wo')
        '4th 1st',
        // Carbon::parse('2018-01-05 00:00:00')->isoFormat('Do wo')
        '5th 1st',
        // Carbon::parse('2018-01-06 00:00:00')->isoFormat('Do wo')
        '6th 1st',
        // Carbon::parse('2018-01-07 00:00:00')->isoFormat('Do wo')
        '7th 1st',
        // Carbon::parse('2018-01-11 00:00:00')->isoFormat('Do wo')
        '11th 2nd',
        // Carbon::parse('2018-02-09 00:00:00')->isoFormat('DDDo')
        '40th',
        // Carbon::parse('2018-02-10 00:00:00')->isoFormat('DDDo')
        '41st',
        // Carbon::parse('2018-04-10 00:00:00')->isoFormat('DDDo')
        '100th',
        // Carbon::parse('2018-02-10 00:00:00', 'Europe/Paris')->isoFormat('h:mm a z')
        '12:00 ip. CET',
        // Carbon::parse('2018-02-10 00:00:00')->isoFormat('h:mm A, h:mm a')
        '12:00 ip., 12:00 ip.',
        // Carbon::parse('2018-02-10 01:30:00')->isoFormat('h:mm A, h:mm a')
        '1:30 ip., 1:30 ip.',
        // Carbon::parse('2018-02-10 02:00:00')->isoFormat('h:mm A, h:mm a')
        '2:00 ip., 2:00 ip.',
        // Carbon::parse('2018-02-10 06:00:00')->isoFormat('h:mm A, h:mm a')
        '6:00 ip., 6:00 ip.',
        // Carbon::parse('2018-02-10 10:00:00')->isoFormat('h:mm A, h:mm a')
        '10:00 ip., 10:00 ip.',
        // Carbon::parse('2018-02-10 12:00:00')->isoFormat('h:mm A, h:mm a')
        '12:00 ep., 12:00 ep.',
        // Carbon::parse('2018-02-10 17:00:00')->isoFormat('h:mm A, h:mm a')
        '5:00 ep., 5:00 ep.',
        // Carbon::parse('2018-02-10 21:30:00')->isoFormat('h:mm A, h:mm a')
        '9:30 ep., 9:30 ep.',
        // Carbon::parse('2018-02-10 23:00:00')->isoFormat('h:mm A, h:mm a')
        '11:00 ep., 11:00 ep.',
        // Carbon::parse('2018-01-01 00:00:00')->ordinal('hour')
        '0th',
        // Carbon::now()->subSeconds(1)->diffForHumans()
        '1 nubbe ago',
        // Carbon::now()->subSeconds(1)->diffForHumans(null, false, true)
        '1 nubbe ago',
        // Carbon::now()->subSeconds(2)->diffForHumans()
        '2 nubbe ago',
        // Carbon::now()->subSeconds(2)->diffForHumans(null, false, true)
        '2 nubbe ago',
        // Carbon::now()->subMinutes(1)->diffForHumans()
        '1 miinut ago',
        // Carbon::now()->subMinutes(1)->diffForHumans(null, false, true)
        '1 miinut ago',
        // Carbon::now()->subMinutes(2)->diffForHumans()
        '2 miinut ago',
        // Carbon::now()->subMinutes(2)->diffForHumans(null, false, true)
        '2 miinut ago',
        // Carbon::now()->subHours(1)->diffForHumans()
        '1 äigi ago',
        // Carbon::now()->subHours(1)->diffForHumans(null, false, true)
        '1 äigi ago',
        // Carbon::now()->subHours(2)->diffForHumans()
        '2 äigi ago',
        // Carbon::now()->subHours(2)->diffForHumans(null, false, true)
        '2 äigi ago',
        // Carbon::now()->subDays(1)->diffForHumans()
        '1 peivi ago',
        // Carbon::now()->subDays(1)->diffForHumans(null, false, true)
        '1 peivi ago',
        // Carbon::now()->subDays(2)->diffForHumans()
        '2 peivi ago',
        // Carbon::now()->subDays(2)->diffForHumans(null, false, true)
        '2 peivi ago',
        // Carbon::now()->subWeeks(1)->diffForHumans()
        '1 okko ago',
        // Carbon::now()->subWeeks(1)->diffForHumans(null, false, true)
        '1 okko ago',
        // Carbon::now()->subWeeks(2)->diffForHumans()
        '2 okko ago',
        // Carbon::now()->subWeeks(2)->diffForHumans(null, false, true)
        '2 okko ago',
        // Carbon::now()->subMonths(1)->diffForHumans()
        '1 mánuppaje ago',
        // Carbon::now()->subMonths(1)->diffForHumans(null, false, true)
        '1 mánuppaje ago',
        // Carbon::now()->subMonths(2)->diffForHumans()
        '2 mánuppaje ago',
        // Carbon::now()->subMonths(2)->diffForHumans(null, false, true)
        '2 mánuppaje ago',
        // Carbon::now()->subYears(1)->diffForHumans()
        '1 ihe ago',
        // Carbon::now()->subYears(1)->diffForHumans(null, false, true)
        '1 ihe ago',
        // Carbon::now()->subYears(2)->diffForHumans()
        '2 ihe ago',
        // Carbon::now()->subYears(2)->diffForHumans(null, false, true)
        '2 ihe ago',
        // Carbon::now()->addSecond()->diffForHumans()
        '1 nubbe from now',
        // Carbon::now()->addSecond()->diffForHumans(null, false, true)
        '1 nubbe from now',
        // Carbon::now()->addSecond()->diffForHumans(Carbon::now())
        '1 nubbe after',
        // Carbon::now()->addSecond()->diffForHumans(Carbon::now(), false, true)
        '1 nubbe after',
        // Carbon::now()->diffForHumans(Carbon::now()->addSecond())
        '1 nubbe before',
        // Carbon::now()->diffForHumans(Carbon::now()->addSecond(), false, true)
        '1 nubbe before',
        // Carbon::now()->addSecond()->diffForHumans(Carbon::now(), true)
        '1 nubbe',
        // Carbon::now()->addSecond()->diffForHumans(Carbon::now(), true, true)
        '1 nubbe',
        // Carbon::now()->diffForHumans(Carbon::now()->addSecond()->addSecond(), true)
        '2 nubbe',
        // Carbon::now()->diffForHumans(Carbon::now()->addSecond()->addSecond(), true, true)
        '2 nubbe',
        // Carbon::now()->addSecond()->diffForHumans(null, false, true, 1)
        '1 nubbe from now',
        // Carbon::now()->addMinute()->addSecond()->diffForHumans(null, true, false, 2)
        '1 miinut 1 nubbe',
        // Carbon::now()->addYears(2)->addMonths(3)->addDay()->addSecond()->diffForHumans(null, true, true, 4)
        '2 ihe 3 mánuppaje 1 peivi 1 nubbe',
        // Carbon::now()->addYears(3)->diffForHumans(null, null, false, 4)
        '3 ihe from now',
        // Carbon::now()->subMonths(5)->diffForHumans(null, null, true, 4)
        '5 mánuppaje ago',
        // Carbon::now()->subYears(2)->subMonths(3)->subDay()->subSecond()->diffForHumans(null, null, true, 4)
        '2 ihe 3 mánuppaje 1 peivi 1 nubbe ago',
        // Carbon::now()->addWeek()->addHours(10)->diffForHumans(null, true, false, 2)
        '1 okko 10 äigi',
        // Carbon::now()->addWeek()->addDays(6)->diffForHumans(null, true, false, 2)
        '1 okko 6 peivi',
        // Carbon::now()->addWeek()->addDays(6)->diffForHumans(null, true, false, 2)
        '1 okko 6 peivi',
        // Carbon::now()->addWeek()->addDays(6)->diffForHumans(["join" => true, "parts" => 2])
        '1 okko and 6 peivi from now',
        // Carbon::now()->addWeeks(2)->addHour()->diffForHumans(null, true, false, 2)
        '2 okko 1 äigi',
        // Carbon::now()->addHour()->diffForHumans(["aUnit" => true])
        '1 äigi from now',
        // CarbonInterval::days(2)->forHumans()
        '2 peivi',
        // CarbonInterval::create('P1DT3H')->forHumans(true)
        '1 peivi 3 äigi',
    ];
}
